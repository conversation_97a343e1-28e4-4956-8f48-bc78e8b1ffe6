package com.ruoyi.system.service.antiblock.impl;

import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.UrlUtils;
import com.ruoyi.system.config.AntiBlockConfig;
import com.ruoyi.system.service.antiblock.AntiBlockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.net.URL;
import java.util.List;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 防风控跳链服务实现
 *
 * <AUTHOR>
 * @date 2025/07/29
 */
@Slf4j
@Service
public class AntiBlockServiceImpl implements AntiBlockService {

    @Autowired
    private AntiBlockConfig antiBlockConfig;

    @Override
    public String selectBestStepDomain(String currentDomain) {
        List<String> stepDomains = antiBlockConfig.getDefaultStepDomains();

        if (CollectionUtils.isEmpty(stepDomains)) {
            log.warn("未配置跳转域名列表，使用当前域名: {}", currentDomain);
            return currentDomain;
        }

        // 过滤掉当前域名，避免使用相同域名
        List<String> availableDomains = stepDomains.stream()
                .filter(domain -> !domain.equals(currentDomain))
                .collect(java.util.stream.Collectors.toList());

        if (availableDomains.isEmpty()) {
            log.warn("所有配置域名都与当前域名相同，使用当前域名: {}", currentDomain);
            return currentDomain;
        }

        // 基于时间和当前域名的哈希值选择域名，确保相对稳定但又有变化
        long timeWindow = System.currentTimeMillis() / (1000L * 60 * 5); // 5分钟时间窗口
        int hash = Math.abs((currentDomain + timeWindow).hashCode());
        String selectedDomain = availableDomains.get(hash % availableDomains.size());

        log.debug("智能选择跳转域名: {} -> {}, 可选域名数量: {}",
                currentDomain, selectedDomain, availableDomains.size());

        return selectedDomain;
    }


    @Override
    public String encryptTargetUrl(String targetUrl) {
        if ( StringUtils.isBlank(targetUrl) || targetUrl.startsWith(ENCRYPTION_PREFIX) ) {
            log.info("targetUrl为空或已加密，targetUrl:{}", targetUrl);
            return targetUrl;
        }
        try {
            String encryptionKey = getCurrentEncryptionKey();
            String encrypted = UrlUtils.encryptTargetUrl(targetUrl, encryptionKey);
            if ( StringUtils.isBlank(encrypted) ) {
                log.error("encryptTargetUrl URL加密失败加密后结果为空, targetUrl:{}", targetUrl);
                return targetUrl;
            }
            encrypted = ENCRYPTION_PREFIX + encrypted;
            return encrypted;
        } catch (Exception e) {
            log.error("encryptTargetUrl URL加密失败: {}", targetUrl, e);
            // 加密失败时返回原URL，确保功能可用性
            return targetUrl;
        }
    }

    @Override
    public String decryptTargetUrl(String encryptedUrl) {
        if ( StringUtils.isBlank(encryptedUrl) ) {
            log.warn("decryptTargetUrl 解密失败待解密URL为空， encryptedUrl:{}", encryptedUrl);
            return null;
        }
        try {
            // 尝试使用当前密钥解密
            String currentKey = getCurrentEncryptionKey();
            String decrypted = UrlUtils.decryptTargetUrl(encryptedUrl, currentKey);

            if ( StringUtils.isBlank(decrypted) ) {
                log.warn("decryptTargetUrl 解密失败解密后URL为空， encryptedUrl:{}, decrypted:{}", encryptedUrl, decrypted);
                return null;
            }
            return decrypted;
        } catch (Exception e) {
            log.error("decryptTargetUrl URL解密失败: {}", encryptedUrl, e);
            return null;
        }
    }

    @Override
    public String getCurrentEncryptionKey() {
        return antiBlockConfig.getEncryption().getSecretKey();
    }
}
