package com.ruoyi.system.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 防风控跳链配置
 *
 * <AUTHOR>
 * @date 2025/07/29
 */
@Data
@Component
@ConfigurationProperties(prefix = "antiblock")
public class AntiBlockConfig {

    /**
     * 是否启用防风控跳链功能
     */
    private boolean enabled = true;

    /**
     * 默认的中间跳转域名列表
     */
    private List<String> defaultStepDomains;

    /**
     * 加密配置
     */
    private Encryption encryption = new Encryption();


    @Data
    public static class Encryption {
        /**
         * 加密密钥（生产环境建议使用复杂密钥）
         */
        private String secretKey = "AntiBlock2025Key";
    }
}
